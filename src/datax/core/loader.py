"""
数据加载器抽象基类模块。

本模块定义了数据加载器的抽象接口，所有具体的数据库加载器都必须继承并实现该接口。
提供了异步上下文管理器支持，确保数据库连接的正确建立和释放。
支持批量数据加载，并提供了表结构准备和冲突处理机制。

主要功能：
- 定义标准的数据加载器接口
- 提供异步上下文管理器支持
- 支持基于 schema 的目标表准备
- 实现批量数据加载机制
- 确保资源的正确管理和释放
"""

from abc import ABC, abstractmethod
from types import TracebackType
from typing import Self

import pandas as pd
import pyarrow as pa

from ..models import ConnectionConfig, ImportConfig


class Loader(ABC):
    """数据加载器接口定义。"""

    def __init__(self, config: ConnectionConfig):
        """
        使用连接配置初始化加载器。

        :param config: 包含数据库连接信息的 Pydantic 模型。
        """
        self.config = config
        self.connection: object | None = None
        self.cursor: object | None = None

    @abstractmethod
    async def _connect(self) -> None:
        """建立数据库连接的内部方法。"""
        raise NotImplementedError

    async def prepare_target(self, schema: pa.Schema, table_name: str) -> None:
        """
        (可选) 在加载数据前准备目标。

        例如，根据 Parquet 文件的 schema 自动创建目标表。
        如果目标表已存在，则此方法可以不执行任何操作。

        :param schema: 源数据的 pyarrow Schema。
        :param table_name: 目标表名。
        """
        import logging

        logger = logging.getLogger(__name__)
        logger.info(
            f"prepare_target called for table '{table_name}' with schema containing "
            f"{len(schema.names)} columns: {', '.join(schema.names)}. "
            "Automatic table creation not implemented yet."
        )

    @abstractmethod
    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """
        将单个数据块加载到目标数据库。

        :param data: 要加载的 pandas DataFrame 数据块。
        :param import_config: 导入配置。
        """
        raise NotImplementedError

    @abstractmethod
    async def close(self) -> None:
        """关闭连接，释放所有数据库资源。"""
        raise NotImplementedError

    async def __aenter__(self) -> Self:
        await self._connect()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        await self.close()
