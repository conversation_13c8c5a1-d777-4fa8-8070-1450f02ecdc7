"""
数据导出流程模块。

本模块使用 Prefect 工作流框架实现数据导出功能。
提供了从数据库提取数据、转换数据、生成 Parquet 文件并上传到 S3 的完整流程。

主要功能：
- stream_to_parquet: 流式处理数据并生成 Parquet 文件
- upload_to_s3: 上传文件到 S3 存储
- export_flow: 统一的数据库导出工作流
"""

import logging
import os
import tempfile
from pathlib import Path

import pyarrow as pa
import pyarrow.parquet as pq
from prefect import flow, task
from pydantic import SecretStr

from ..core.factories import get_extractor, get_translator
from ..models.config import ExportConfig, S3Config

logger = logging.getLogger(__name__)


@task
def stream_to_parquet(config: ExportConfig, output_dir: str) -> list[str]:
    """
    一个 Prefect 任务，负责驱动从提取、转换到写入 Parquet 文件的整个数据流。

    参数:
        config: 导出配置对象
        output_dir: Parquet 文件输出目录

    返回:
        list[str]: 生成的 Parquet 文件路径列表
    """
    extractor = get_extractor(config.source_db)
    translator = get_translator(config.translator)

    output_files = []
    file_counter = 0
    rows_in_current_file = 0
    writer: pq.ParquetWriter | None = None
    current_file_path = ""

    try:
        with extractor:
            data_stream = extractor.extract_stream(config.query, config)
            transformed_stream = translator.transform_stream(data_stream)

            for i, df_chunk in enumerate(transformed_stream):
                if df_chunk.empty:
                    continue

                table_chunk = pa.Table.from_pandas(df_chunk, preserve_index=False)

                if writer is None or (
                    config.rows_per_file
                    and rows_in_current_file >= config.rows_per_file
                ):
                    if writer:
                        writer.close()

                    file_counter += 1
                    current_file_path = os.path.join(
                        output_dir, f"part-{file_counter:05d}.parquet"
                    )
                    writer = pq.ParquetWriter(current_file_path, table_chunk.schema)
                    output_files.append(current_file_path)
                    rows_in_current_file = 0

                writer.write_table(table_chunk)
                rows_in_current_file += len(df_chunk)
                logger.info(
                    f"Processed chunk {i}, written {len(df_chunk)} rows to {current_file_path}."
                )

    finally:
        if writer:
            writer.close()
            logger.info(f"Closed Parquet writer for {current_file_path}.")

    return output_files


@task(retries=3, retry_delay_seconds=60)
def upload_to_s3(local_path: str, s3_config: S3Config, part_key: str):
    """
    将单个文件上传到 S3。

    参数:
        local_path: 本地文件路径
        s3_config: S3 配置对象
        part_key: S3 对象键名
    """
    # ... 在这里实现 boto3 上传逻辑 ...
    logger.info(
        f"Simulating upload of {local_path} to s3://{s3_config.bucket}/{part_key}"
    )


@flow(name="Unified Database Export Flow", log_prints=True)
def export_flow(config: ExportConfig):
    """
    一个统一的数据库 ETL 工作流。

    根据配置从源数据库提取数据，进行转换，生成 Parquet 文件，并上传到 S3。

    参数:
        config: 导出配置对象，包含源数据库、查询、S3 目标等配置
    """
    # 使用临时目录来存放生成的 Parquet 文件
    with tempfile.TemporaryDirectory() as tmpdir:
        print(f"临时文件将存储在: {tmpdir}")

        local_files = stream_to_parquet(config, tmpdir)

        if not local_files:
            print("没有数据被导出，流程结束。")
            return

        print(f"成功生成 {len(local_files)} 个 Parquet 文件，准备上传...")

        # 并行上传文件
        for local_file in local_files:
            file_name = Path(local_file).name
            # 构造 S3 上的最终文件路径
            s3_key = f"{config.s3_target.key.rstrip('/')}/{file_name}"
            # 此处可以并行提交任务以提高效率
            upload_to_s3.submit(local_file, config.s3_target, s3_key)

    print("导出流程执行完毕。")


if __name__ == "__main__":
    # 示例: 如何在本地运行这个 Flow
    from ..models.config import PostgresConfig, QueryConfig

    # 注意：运行此示例需要一个本地运行的 PostgreSQL 实例
    pg_config = PostgresConfig(
        user="testuser",
        password=SecretStr("testpass"),
        host="localhost",
        port=5432,
        database="testdb",
        schema="public",
    )

    query_conf = QueryConfig(table="sample_data", limit=5000)

    s3_conf = S3Config(bucket="my-data-bucket", key="exports/postgres/sample_data")

    full_config = ExportConfig(
        source_db=pg_config,
        query=query_conf,
        s3_target=s3_conf,
        parquet_chunk_size=1000,
        rows_per_file=2000,
    )

    export_flow(config=full_config)
