"""
MySQL 连接器模块。

本模块提供了 MySQL 数据库的提取器和加载器实现。
支持异步操作，使用服务器端游标进行流式数据处理，适合处理大规模数据集。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- MySQLConnector: 共享的连接管理基类
- MySQLExtractor: 使用服务器端游标的流式数据提取器
- MySQLLoader: 批量数据加载器，支持冲突处理策略
- 支持自定义初始化 SQL 命令
- 支持 REPLACE INTO 和 INSERT IGNORE 策略
"""

import logging
from collections.abc import AsyncIterator
from typing import Any, override

import asyncmy
import asyncmy.cursors  # type: ignore
import pandas as pd
from asyncmy import Connection as MySQLConnection

from src.datax.models import ImportConfig

from ..core import Extractor, Loader
from ..models import (
    ConflictStrategy,
    ExportConfig,
    MySQLConfig,
    QueryConfig,
)

logger = logging.getLogger(__name__)


class MySQLConnector:
    """MySQL 提取器和加载器的共享连接逻辑。"""

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 连接器。

        参数:
            config: MySQL 配置对象
        """
        self.config = config
        self.connection: MySQLConnection | None = None

    async def _connect(self) -> None:
        """建立并配置 MySQL 连接。"""
        if self.connection and not self.connection.closed:
            return

        try:
            # 使用配置中的init_sql参数
            init_command = self.config.init_sql if self.config.init_sql else None

            self.connection = await asyncmy.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.get_password_value(),
                database=self.config.database,
                charset="utf8mb4",
                init_command=init_command,
            )
            logger.debug("MySQL connection established.")

        except Exception as e:
            logger.error(f"MySQL connection failed: {e}")
            raise

    async def close(self) -> None:
        """关闭数据库连接。"""
        if self.connection:
            try:
                await self.connection.close()
            except Exception as e:
                logger.error(f"Error closing MySQL connection: {e}")
            finally:
                self.connection = None
                logger.debug("MySQL connection closed.")


class MySQLExtractor(MySQLConnector, Extractor):
    """使用服务器端游标的 MySQL 数据提取器。"""

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 提取器。

        参数:
            config: MySQL 配置对象
        """
        MySQLConnector.__init__(self, config)
        Extractor.__init__(self, config)

    def _build_query(self, query_config: QueryConfig) -> str:
        """
        根据 QueryConfig 模型构建 SQL 查询语句。

        参数:
            query_config: 查询配置对象

        返回:
            str: 构建的 SQL 查询语句
        """
        if query_config.sql_query:
            logger.info("Using provided SQL query.")
            return query_config.sql_query

        cols = (
            ", ".join(f"`{c}`" for c in query_config.columns)
            if query_config.columns
            else "*"
        )
        query = f"SELECT {cols} FROM `{query_config.table}`"
        if query_config.limit:
            query += f" LIMIT {query_config.limit}"
        return query

    @override
    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """
        使用服务器端游标（SSDictCursor）流式提取数据。

        参数:
            query_config: 查询配置对象
            export_config: 导出配置对象

        Yields:
            pd.DataFrame: 数据块
        """
        await self._connect()
        if not self.connection:
            raise ConnectionError("MySQL connection not established.")

        try:
            # 使用服务器端游标进行流式处理
            async with self.connection.cursor(asyncmy.cursors.SSDictCursor) as cursor:
                query = self._build_query(query_config)
                logger.info(f"Executing query with server-side cursor: {query}")
                await cursor.execute(query)

                buffer: list[dict[str, Any]] = []
                while True:
                    # 从服务器端游标获取一小批数据
                    rows = await cursor.fetchmany(size=export_config.cursor_fetch_size)
                    if not rows:
                        break

                    buffer.extend(rows)
                    if len(buffer) >= export_config.parquet_chunk_size:
                        yield pd.DataFrame(buffer)
                        buffer.clear()

                if buffer:
                    yield pd.DataFrame(buffer)
        finally:
            await self.close()


class MySQLLoader(MySQLConnector, Loader):
    """MySQL 数据加载器。"""

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 加载器。

        参数:
            config: MySQL 配置对象
        """
        MySQLConnector.__init__(self, config)
        Loader.__init__(self, config)

    @override
    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """
        将 DataFrame 数据块加载到 MySQL。

        参数:
            data: 要加载的数据块
            import_config: 导入配置对象
        """
        await self._connect()
        if not self.connection or data.empty:
            return

        # Convert DataFrame to a list of tuples for executemany
        records = data.to_records(index=False).tolist()

        cols = ", ".join(f"`{col}`" for col in data.columns)
        placeholders = ", ".join(["%s"] * len(data.columns))

        if import_config.conflict_strategy == ConflictStrategy.REPLACE:
            # REPLACE INTO is a MySQL-specific extension.
            # Warning: This is a DELETE then INSERT operation, which can have
            # side effects like firing ON DELETE triggers.
            sql = f"REPLACE INTO `{import_config.table_name}` ({cols}) VALUES ({placeholders})"
        else:  # 'ignore'
            sql = f"INSERT IGNORE INTO `{import_config.table_name}` ({cols}) VALUES ({placeholders})"

        async with self.connection.cursor() as cursor:
            try:
                await cursor.executemany(sql, records)
                await self.connection.commit()
                logger.info(
                    f"Loaded {cursor.rowcount} rows into '{import_config.table_name}' using '{import_config.conflict_strategy}' strategy."
                )
            except Exception as e:
                logger.error(f"Error loading data into MySQL: {e}")
                await self.connection.rollback()
                raise
