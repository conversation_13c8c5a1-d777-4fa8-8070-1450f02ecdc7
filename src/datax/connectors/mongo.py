"""
MongoDB 连接器模块。

本模块提供了 MongoDB 数据库的提取器和加载器实现。
支持异步操作，使用 motor 库进行高性能数据处理。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- MongoConnector: 共享的连接管理基类
- MongoExtractor: 流式数据提取器，支持游标分批读取
- MongoLoader: 批量数据加载器，支持 upsert 操作
- 支持自定义查询和投影
- 支持 ObjectId 类型处理
"""

import json
import logging
from collections.abc import AsyncIterator
from typing import Any, override

import pandas as pd
import pyarrow as pa
from bson import ObjectId  # type: ignore
from motor.motor_asyncio import AsyncIOMotorClient  # type: ignore

from ..core import Extractor, Loader
from ..models import (
    ConflictStrategy,
    ExportConfig,
    ImportConfig,
    MongoConfig,
    QueryConfig,
)

logger = logging.getLogger(__name__)


class MongoConnector:
    """MongoDB 共享连接逻辑。"""

    def __init__(self, config: MongoConfig):
        """
        初始化 MongoDB 连接器。

        参数:
            config: MongoDB 配置对象，包含连接参数
        """
        self.config = config
        self.connection: AsyncIOMotorClient | None = None
        self.database: Any | None = None
        logger.debug("MongoConnector initialized with config")

    async def _connect(self) -> None:
        """
        建立并配置 MongoDB 连接。

        使用配置中的参数创建新的 MongoDB 客户端连接。

        异常:
            PyMongoError: 连接失败时抛出
        """
        if self.connection:
            # Connection already exists
            logger.debug("MongoDB connection already established")
            return

        try:
            # Build connection URI
            auth_part = ""
            if self.config.username and self.config.password:
                password = self.config.get_password_value()
                auth_part = f"{self.config.username}:{password}@"

            # Use connection_string if provided, otherwise build from components
            if self.config.connection_string:
                uri = self.config.connection_string.get_secret_value()
            else:
                uri = f"mongodb://{auth_part}{self.config.host}:{self.config.port}/{self.config.database}"

            # Create MongoDB client
            self.connection = AsyncIOMotorClient(uri, serverSelectionTimeoutMS=5000)

            # Test connection by pinging the server
            await self.connection.admin.command("ping")

            # Get database reference
            self.database = self.connection[self.config.database]

            logger.info(
                f"MongoDB connection established to {self.config.host}:{self.config.port}/{self.config.database}"
            )

            # Execute optional initialization commands
            if self.config.init_sql:
                # For MongoDB, init_sql could be JavaScript code or admin commands
                # This is a simplified implementation - in practice you might want to
                # parse and execute specific MongoDB commands
                logger.warning(
                    f"MongoDB init_sql not fully implemented: {self.config.init_sql}"
                )
                logger.debug("init_sql skipped for MongoDB (not implemented)")

        except Exception as e:
            logger.error(f"Unexpected error during MongoDB connection: {e}")
            self.connection = None
            self.database = None
            raise

    async def close(self) -> None:
        """关闭 MongoDB 客户端连接。"""
        if self.connection:
            try:
                self.connection.close()
                logger.debug("MongoDB connection closed")
            except Exception as e:
                logger.error(f"Error during MongoDB disconnect: {e}")
            finally:
                # 始终将连接设置为 None
                self.connection = None
                self.database = None
                logger.debug("MongoDB connection cleanup completed")


class MongoExtractor(MongoConnector, Extractor):  # type: ignore
    """使用游标迭代的 MongoDB 数据提取器。"""

    def __init__(self, config: MongoConfig):
        """
        初始化 MongoDB 提取器。

        参数:
            config: MongoDB 配置对象
        """
        MongoConnector.__init__(self, config)
        Extractor.__init__(self, config)

    def _build_query(
        self, query_config: QueryConfig
    ) -> tuple[dict[str, Any], dict[str, Any]]:
        """
        根据 QueryConfig 构建 MongoDB 查询和投影。

        参数:
            query_config: 包含集合和查询参数的 QueryConfig

        返回:
            tuple: (filter_dict, projection_dict) 用于 MongoDB find 操作
        """
        # MongoDB uses filter documents instead of SQL WHERE clauses
        filter_dict: dict[str, Any] = {}

        # If sql_query is provided, try to parse it as JSON filter
        if query_config.sql_query:
            logger.info("Using provided query as MongoDB filter")
            try:
                filter_dict = json.loads(query_config.sql_query)
            except json.JSONDecodeError:
                logger.warning(
                    f"Invalid JSON in sql_query, using empty filter: {query_config.sql_query}"
                )
                filter_dict = {}

        # Build projection dict from columns
        projection_dict: dict[str, Any] = {}
        if query_config.columns:
            # Include specified columns, exclude _id by default unless explicitly included
            for col in query_config.columns:
                projection_dict[col] = 1
            if "_id" not in query_config.columns:
                projection_dict["_id"] = 0

        # Handle time_range if provided
        if query_config.time_range:
            start_time, end_time = query_config.time_range
            # Assume there's a timestamp field - this could be configurable
            filter_dict["timestamp"] = {"$gte": start_time, "$lte": end_time}
            logger.info(f"Added time range filter: {start_time} to {end_time}")

        return filter_dict, projection_dict

    @override
    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """
        使用 MongoDB 游标迭代进行流式数据提取。

        参数:
            query_config: 查询配置对象
            export_config: 导出配置对象

        Yields:
            pd.DataFrame: 数据块
        """
        await self._connect()
        if not self.connection or not self.database:
            raise ConnectionError("MongoDB connection not established.")

        # Get collection
        collection = self.database[query_config.table]

        # Build query and projection
        filter_dict, projection_dict = self._build_query(query_config)

        logger.info(
            f"Executing MongoDB query on collection '{query_config.table}': filter={filter_dict}, projection={projection_dict}"
        )

        buffer: list[dict[str, Any]] = []

        try:
            # Create cursor with batch size
            cursor = collection.find(filter_dict, projection_dict)

            # Apply limit if specified
            if query_config.limit:
                cursor = cursor.limit(query_config.limit)

            # Set batch size for efficient streaming
            cursor = cursor.batch_size(export_config.cursor_fetch_size)

            async for document in cursor:
                # Convert ObjectId to string for JSON serialization
                processed_doc = self._process_document(document)
                buffer.append(processed_doc)

                if len(buffer) >= export_config.parquet_chunk_size:
                    logger.debug(f"Yielding MongoDB chunk with {len(buffer)} rows")
                    yield pd.DataFrame(buffer)
                    buffer.clear()

            # Yield any remaining rows in the buffer
            if buffer:
                logger.debug(f"Yielding final MongoDB chunk with {len(buffer)} rows")
                yield pd.DataFrame(buffer)

        finally:
            await self.close()

    def _process_document(self, document: dict[str, Any]) -> dict[str, Any]:
        """
        处理 MongoDB 文档以兼容 DataFrame。

        将 ObjectId 和其他 BSON 类型转换为可 JSON 序列化的格式。

        参数:
            document: 原始 MongoDB 文档

        返回:
            dict: 处理后的包含可序列化值的文档
        """
        processed = {}

        for key, value in document.items():
            if isinstance(value, ObjectId):
                # Convert ObjectId to string
                processed[key] = str(value)
            elif isinstance(value, dict):
                # Recursively process nested documents
                processed[key] = self._process_document(value)
            elif isinstance(value, list):
                # Process arrays/lists
                processed[key] = [
                    self._process_document(item)
                    if isinstance(item, dict)
                    else str(item)
                    if isinstance(item, ObjectId)
                    else item
                    for item in value
                ]
            else:
                # Keep other types as-is
                processed[key] = value

        return processed


class MongoLoader(MongoConnector, Loader):  # type: ignore
    """MongoDB 数据加载器。"""

    def __init__(self, config: MongoConfig):
        """
        初始化 MongoDB 加载器。

        参数:
            config: MongoDB 配置对象
        """
        MongoConnector.__init__(self, config)
        Loader.__init__(self, config)

    @override
    async def prepare_target(self, schema: pa.Schema, table_name: str) -> None:
        """
        准备目标集合（如需要）。

        MongoDB 集合在插入第一个文档时自动创建。
        此方法记录关于 schema 的信息但不创建集合。

        参数:
            schema: 数据的 pyarrow Schema
            table_name: 目标集合名
        """
        # Call parent implementation for logging
        await super().prepare_target(schema, table_name)
        logger.info(
            f"MongoDB prepare_target: Collection '{table_name}' will be created automatically on first insert. "
            "Consider creating indexes manually for better performance."
        )

    @override
    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """
        将 DataFrame 数据块加载到 MongoDB。

        参数:
            data: 要加载的 DataFrame
            import_config: 导入配置对象
        """
        # Early return on empty DataFrame
        if data.empty:
            logger.debug("Empty DataFrame provided, skipping load")
            return

        await self._connect()
        if not self.connection or not self.database:
            raise ConnectionError("MongoDB connection not established")

        table_name = import_config.table_name
        # Get collection
        collection = self.database[table_name]

        # Convert DataFrame to list of documents
        documents = data.to_dict("records")
        strategy = import_config.conflict_strategy
        try:
            if strategy == ConflictStrategy.IGNORE:
                # For ignore strategy, perform plain insert
                # MongoDB will handle duplicate key errors automatically
                result = await collection.insert_many(documents)
            else:
                # For update strategy, perform upsert operation
                # This is a simplified implementation - in practice you might want
                # to use specific update operations based on unique fields
                logger.warning(
                    f"Update strategy for MongoDB collection '{table_name}' uses simple upsert. "
                    "Consider implementing proper update logic based on unique fields."
                )
                result = await collection.insert_many(documents)

            logger.info(
                f"Loaded {len(result.inserted_ids)} documents into MongoDB collection '{table_name}' using '{strategy}' strategy."
            )

        except Exception as e:
            logger.error(
                f"Error loading data into MongoDB collection '{table_name}': {e}"
            )
            raise
