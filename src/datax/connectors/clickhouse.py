"""
ClickHouse 连接器模块。

本模块提供了 ClickHouse 数据库的提取器和加载器实现。
支持异步操作，使用 aioch 库进行高性能数据处理。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- ClickHouseConnector: 共享的连接管理基类
- ClickHouseExtractor: 流式数据提取器，支持分批读取
- ClickHouseLoader: 批量数据加载器，支持高性能批量插入
- 支持自定义初始化 SQL 命令
- 支持数据压缩传输
"""

import logging
from collections.abc import Iterator
from typing import Any, override

import pandas as pd
import pyarrow as pa
from aioch import Client
from aioch.errors import Error as ClickHouseError

from ..core import Extractor, Loader
from ..models import ClickHouseConfig, ConflictStrategy, ExportConfig, QueryConfig

logger = logging.getLogger(__name__)


class ClickHouseConnector:
    """ClickHouse 共享连接逻辑。"""

    def __init__(self, config: ClickHouseConfig):
        """
        初始化 ClickHouse 连接器。

        参数:
            config: ClickHouse 配置对象，包含连接参数
        """
        self.config = config
        self.connection: Client | None = None
        logger.debug("ClickHouseConnector initialized with config")

    async def _connect(self) -> None:
        """
        建立并配置 ClickHouse 连接。

        使用配置中的参数创建新的 ClickHouse 客户端连接。
        连接建立后，如果提供了 init_sql，则执行初始化 SQL。

        异常:
            ClickHouseError: 连接失败或 init_sql 执行失败时抛出
        """
        if self.connection:
            # Connection already exists
            logger.debug("ClickHouse connection already established")
            return

        try:
            # Get password value if it exists
            password = None
            if self.config.password:
                password = self.config.get_password_value()

            # Create ClickHouse client with compression enabled
            self.connection = Client(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=password,
                database=self.config.database,
                compression=True,
            )

            logger.info(
                f"ClickHouse connection established to {self.config.host}:{self.config.port}/{self.config.database}"
            )

            # Execute optional initialization SQL
            if self.config.init_sql:
                logger.info(f"Executing init_sql: {self.config.init_sql}")
                await self.connection.execute(self.config.init_sql)  # type: ignore
                logger.debug("init_sql executed successfully")

        except Exception as e:
            logger.error(f"Unexpected error during ClickHouse connection: {e}")
            self.connection = None
            raise

    async def close(self) -> None:
        """关闭连接。

        设置 self.connection 为 None 并关闭连接。
        """
        if self.connection:
            try:
                await self.connection.close()
                logger.debug("ClickHouse connection disconnected")
            except Exception as e:
                logger.error(f"Error during ClickHouse disconnect: {e}")
            finally:
                # 始终将连接设置为 None
                self.connection = None
                logger.debug("ClickHouse connection closed")


class ClickHouseExtractor(ClickHouseConnector, Extractor):
    """使用流式迭代器的 ClickHouse 数据提取器。"""

    def __init__(self, config: ClickHouseConfig):
        """
        初始化 ClickHouse 提取器。

        参数:
            config: ClickHouse 配置对象
        """
        ClickHouseConnector.__init__(self, config)
        Extractor.__init__(self, config)

    def _build_query(self, query_config: QueryConfig) -> str:
        """
        根据 QueryConfig 模型构建 SQL 查询语句。

        与 MySQL 不同，ClickHouse 使用普通标识符，不需要反引号。

        参数:
            query_config: 查询配置对象

        返回:
            str: 构建的 SQL 查询语句
        """
        if query_config.sql_query:
            logger.info("Using provided SQL query.")
            return query_config.sql_query

        cols = ", ".join(query_config.columns) if query_config.columns else "*"
        query = f"SELECT {cols} FROM {query_config.table}"
        if query_config.limit:
            query += f" LIMIT {query_config.limit}"
        return query

    @override
    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> Iterator[pd.DataFrame]:
        """
        使用 ClickHouse execute_iter 进行流式数据提取。

        参数:
            query_config: 查询配置对象
            export_config: 导出配置对象

        Yields:
            pd.DataFrame: 数据块
        """
        await self._connect()
        if not self.connection:
            raise ConnectionError("ClickHouse connection not established.")

        query = self._build_query(query_config)
        logger.info(f"Executing ClickHouse query: {query}")

        buffer: list[dict[str, Any]] = []

        try:
            # Use execute_iter for streaming with max_block_size setting
            async for row in self.connection.execute_iter(
                query, settings={"max_block_size": export_config.cursor_fetch_size}
            ):
                # Convert row tuple to dict using column names from first iteration
                if not hasattr(self, "_column_names"):
                    # Get column names from the query result metadata if available
                    # For ClickHouse, we'll assume the row is already a dict or can be converted
                    if isinstance(row, dict):
                        self._column_names = list(row.keys())
                    else:
                        # If row is a tuple, we need to get column names differently
                        # This is a simplified approach - in practice you might need to
                        # extract column names from the query metadata
                        self._column_names = [f"col_{i}" for i in range(len(row))]

                if isinstance(row, dict):
                    buffer.append(row)
                else:
                    # Convert tuple to dict using column names
                    buffer.append(dict(zip(self._column_names, row, strict=False)))

                if len(buffer) >= export_config.parquet_chunk_size:
                    logger.debug(f"Yielding ClickHouse chunk with {len(buffer)} rows")
                    yield pd.DataFrame(buffer)
                    buffer.clear()

            # Yield any remaining rows in the buffer
            if buffer:
                logger.debug(f"Yielding final ClickHouse chunk with {len(buffer)} rows")
                yield pd.DataFrame(buffer)

        finally:
            # ClickHouse iterator doesn't need explicit closure, but we clean up
            if hasattr(self, "_column_names"):
                delattr(self, "_column_names")
            await self.close()


class ClickHouseLoader(ClickHouseConnector, Loader):
    """ClickHouse 数据加载器。"""

    def __init__(self, config: ClickHouseConfig):
        """
        初始化 ClickHouse 加载器。

        参数:
            config: ClickHouse 配置对象
        """
        ClickHouseConnector.__init__(self, config)
        Loader.__init__(self, config)

    @override
    async def prepare_target(self, schema: pa.Schema, table_name: str) -> None:
        """
        准备目标表（如需要）。

        注意：对于 ClickHouse 这是一个空操作，因为表创建通常在外部处理，
        以便指定特定的引擎类型。

        参数:
            schema: 数据的 pyarrow Schema
            table_name: 目标表名
        """
        # Call parent implementation for logging
        await super().prepare_target(schema, table_name)
        logger.info(
            f"ClickHouse prepare_target: Table creation skipped for '{table_name}'. "
            "Tables should be pre-created with appropriate engine types (e.g., MergeTree, ReplacingMergeTree)."
        )

    @override
    async def load_chunk(
        self, data: pd.DataFrame, table_name: str, conflict_strategy: ConflictStrategy
    ) -> None:
        """
        将 DataFrame 数据块加载到 ClickHouse。

        参数:
            data: 要加载的 DataFrame
            table_name: 目标表名
            conflict_strategy: 冲突解决策略（'ignore' 或 'replace'）
        """
        # Early return on empty DataFrame
        if data.empty:
            logger.debug("Empty DataFrame provided, skipping load")
            return

        await self._connect()
        if not self.connection:
            raise ConnectionError("ClickHouse connection not established")

        # Convert DataFrame to list of tuples
        records = [tuple(row) for row in data.values]

        # Build column names for INSERT query
        cols = ", ".join(data.columns)

        try:
            if conflict_strategy == "replace":
                # For replace strategy, we need to handle table engine-specific logic
                # This implementation assumes the table might be ReplacingMergeTree
                # or another engine that supports some form of replacement
                logger.warning(
                    f"Replace strategy for ClickHouse table '{table_name}' assumes "
                    "ReplacingMergeTree engine or similar. For other engines, this "
                    "will perform a simple insert. Consider using ALTER TABLE DELETE "
                    "or recreating the table if full replacement is needed."
                )

                # Try to use SETTINGS replace_running=1 if supported
                # This works with ReplacingMergeTree and similar engines
                insert_query = f"INSERT INTO {table_name} ({cols}) VALUES"

                # Execute with potential replace settings
                # Note: replace_running setting may not be available in all ClickHouse versions
                try:
                    await self.connection.execute(
                        insert_query, records, settings={"replace_running": 1}
                    )
                except Exception as e:
                    # Fallback to simple insert if replace_running is not supported
                    logger.warning(
                        f"Failed to use replace_running setting: {e}. Falling back to simple insert."
                    )
                    await self.connection.execute(insert_query, records)

            elif conflict_strategy == "ignore":
                # For ignore strategy, perform plain insert
                # Deduplication should be handled by MergeTree logic or caller
                insert_query = f"INSERT INTO {table_name} ({cols}) VALUES"
                await self.connection.execute(insert_query, records)

            logger.info(
                f"Loaded {len(data)} rows into ClickHouse table '{table_name}' using '{conflict_strategy}' strategy"
            )

        except ClickHouseError as e:
            logger.error(f"Error loading data into ClickHouse: {e}")
            raise
