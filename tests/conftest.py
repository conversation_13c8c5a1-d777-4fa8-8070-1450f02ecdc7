import os
from collections.abc import AsyncGenerator

import aioboto3
import pytest
import pytest_asyncio
from aioch import Client
from motor.motor_asyncio import AsyncIOMotorClient
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from testcontainers.clickhouse import <PERSON>lick<PERSON>ouse<PERSON>ontainer
from testcontainers.minio import MinioContainer
from testcontainers.mongodb import MongoDbContainer
from testcontainers.mysql import MySqlContainer
from testcontainers.postgres import PostgresContainer

docker_sock = f"unix://{os.path.expanduser('~')}/.docker/run/docker.sock"
os.environ["DOCKER_HOST"] = docker_sock


@pytest_asyncio.fixture(scope="module")
async def postgres_engine(
    request: pytest.FixtureRequest,
) -> AsyncGenerator[AsyncEngine]:
    """
    基于 PostgreSQL 容器，创建一个异步 SQLAlchemy 引擎。
    使用 asyncpg 驱动进行异步数据库操作。
    """
    # 检查是否有 postgres 标记
    markers = request.node.iter_markers(name="postgres")
    if not any(markers):
        pytest.skip("Test not marked with @pytest.mark.postgres")

    with PostgresContainer("postgres:17-alpine") as container:
        # 将同步 URL 转换为异步 URL (使用 asyncpg)
        sync_url = container.get_connection_url()
        print(f"Original URL: {sync_url}")
        # testcontainers 返回的 URL 可能包含 psycopg2 驱动，需要替换
        if "postgresql+psycopg2://" in sync_url:
            async_url = sync_url.replace(
                "postgresql+psycopg2://", "postgresql+asyncpg://"
            )
        else:
            async_url = sync_url.replace("postgresql://", "postgresql+asyncpg://")
        print(f"Async URL: {async_url}")

        engine = create_async_engine(async_url)
        yield engine
        await engine.dispose()


@pytest_asyncio.fixture(scope="module")
async def mysql_engine(request: pytest.FixtureRequest) -> AsyncGenerator[AsyncEngine]:
    """
    基于 MySQL 容器，创建一个异步 SQLAlchemy 引擎。
    使用 asyncmy 驱动进行异步数据库操作。
    """
    # 检查是否有 mysql 标记
    markers = request.node.iter_markers(name="mysql")
    if not any(markers):
        pytest.skip("Test not marked with @pytest.mark.mysql")

    with MySqlContainer("mysql:8") as container:
        container.with_bind_ports(3306, 3306)
        # 将同步 URL 转换为异步 URL (使用 asyncmy)
        sync_url = container.get_connection_url()
        async_url = sync_url.replace("mysql+pymysql://", "mysql+asyncmy://")
        engine = create_async_engine(async_url)
        yield engine
        await engine.dispose()


@pytest_asyncio.fixture(scope="module")
async def clickhouse_client(request: pytest.FixtureRequest) -> AsyncGenerator[Client]:
    """
    基于 ClickHouse 容器，创建一个异步客户端。
    使用 aioch 驱动进行异步数据库操作。
    """
    # 检查是否有 clickhouse 标记
    markers = request.node.iter_markers(name="clickhouse")
    if not any(markers):
        pytest.skip("Test not marked with @pytest.mark.clickhouse")

    with ClickHouseContainer("clickhouse/clickhouse-server:25-alpine") as container:
        container.with_bind_ports(9000, 9000)
        container.with_bind_ports(8123, 8123)

        host = container.get_container_host_ip()
        port = container.get_exposed_port(9000)

        client = Client(host=host, port=port)
        yield client


@pytest_asyncio.fixture(scope="module")
async def mongodb_client(
    request: pytest.FixtureRequest,
) -> AsyncGenerator[AsyncIOMotorClient]:
    """
    基于 MongoDB 容器，创建一个异步客户端。
    使用 motor 驱动进行异步数据库操作。
    只有当测试用例标记了 @pytest.mark.mongodb 时才会创建客户端。
    """
    # 检查是否有 mongodb 标记
    markers = request.node.iter_markers(name="mongodb")
    if not any(markers):
        pytest.skip("Test not marked with @pytest.mark.mongodb")

    with MongoDbContainer("mongo:8") as container:
        container.with_bind_ports(27017, 27017)

        connection_url = container.get_connection_url()
        client = AsyncIOMotorClient(connection_url)

        # 测试连接
        await client.admin.command("ping")

        yield client
        client.close()


@pytest_asyncio.fixture(scope="module")
async def minio_client(
    request: pytest.FixtureRequest,
) -> AsyncGenerator[aioboto3.Session.client]:
    """
    基于 MinIO 容器，创建一个异步 S3 客户端。
    使用 aioboto3 进行异步 S3 操作。
    """

    # 检查是否有 minio 标记
    markers = request.node.iter_markers(name="minio")
    if not any(markers):
        pytest.skip("Test not marked with @pytest.mark.minio")

    with MinioContainer("minio/minio:latest") as container:
        container.with_bind_ports(9000, 9000)

        # 获取 MinIO 配置
        config = container.get_config()
        endpoint_url = f"http://{container.get_container_host_ip()}:{container.get_exposed_port(9000)}"

        # 创建异步 S3 客户端
        session = aioboto3.Session()
        client = await session.client(
            "s3",
            endpoint_url=endpoint_url,
            aws_access_key_id=config["access_key"],
            aws_secret_access_key=config["secret_key"],
            use_ssl=False,
        ).__aenter__()

        yield client
        await client.__aexit__(None, None, None)
