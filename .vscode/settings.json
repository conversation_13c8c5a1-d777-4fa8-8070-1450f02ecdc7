{"python.venvPath": "${workspaceFolder}/.venv", "python.defaultInterpreterPath": "${workspaceFolder}/.venv/bin/python", "python.terminal.activateEnvironment": true, "python.analysis.extraPaths": ["${workspaceFolder}/src"], "python.formatting.provider": "ruff", "editor.formatOnSave": true, "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "python.analysis.autoSearchPaths": true, "python.analysis.diagnosticMode": "openFilesOnly", "python.analysis.stubPath": "${workspaceFolder}/typings", "python.analysis.include": ["src/**/*.py"], "python.analysis.exclude": ["**/node_modules", "**/__pycache__", "**/.git", "**/.venv", "**/tests", "**/typestubs", "**/stubs", "**/experimental", "**/oldstuff", "**/typings"]}