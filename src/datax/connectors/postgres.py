"""
PostgreSQL 连接器模块。

本模块提供了 PostgreSQL 数据库的提取器和加载器实现。
支持异步操作，使用 asyncpg 库进行高性能数据处理。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- PostgresConnector: 共享的连接管理基类
- PostgresExtractor: 流式数据提取器，支持游标分批读取
- PostgresLoader: 批量数据加载器，支持 ON CONFLICT 冲突处理
- 支持自定义初始化 SQL 命令
- 支持 COPY FROM 高性能批量导入
"""

import logging
from collections.abc import AsyncIterator
from typing import Any, override

import asyncpg  # type: ignore
import pandas as pd
from asyncpg import Connection as PgConnection

from ..core import Extractor, Loader
from ..models import (
    ConflictStrategy,
    ExportConfig,
    ImportConfig,
    PostgresConfig,
    QueryConfig,
)

logger = logging.getLogger(__name__)


class PostgresConnector:
    """PostgreSQL 共享连接逻辑。"""

    def __init__(self, config: PostgresConfig):
        self.config = config
        self.connection: PgConnection | None = None

    async def _connect(self) -> None:
        """建立并配置 PostgreSQL 连接。"""
        if self.connection and not self.connection.is_closed():
            return

        try:
            # 构建连接参数
            connection_params = {
                "database": self.config.database,
                "user": self.config.username,
                "password": self.config.get_password_value(),
                "host": self.config.host,
                "port": self.config.port,
            }

            # 如果有init_sql，添加到连接参数中
            if self.config.init_sql:
                connection_params["command_timeout"] = 60  # 设置命令超时
                connection_params["server_settings"] = {
                    "application_name": "datax-ETL",
                    "client_encoding": "UTF8",
                }

            self.connection = await asyncpg.connect(**connection_params)

            # 如果有init_sql，在连接建立后执行
            if self.config.init_sql:
                await self.connection.execute(self.config.init_sql)  # type:ignore
                logger.info(f"Executed init_sql: {self.config.init_sql}")

            logger.debug("PostgreSQL connection established.")

        except asyncpg.PostgresError as e:
            logger.error(f"PostgreSQL connection failed: {e}")
            raise

    async def close(self) -> None:
        """关闭连接。"""
        if self.connection:
            try:
                await self.connection.close()
                self.connection = None
                logger.debug("PostgreSQL connection closed.")
            except asyncpg.PostgresError as e:
                logger.error(f"Error closing PostgreSQL connection: {e}")


class PostgresExtractor(PostgresConnector, Extractor):
    """PostgreSQL 数据提取器。"""

    def __init__(self, config: PostgresConfig):
        PostgresConnector.__init__(self, config)
        Extractor.__init__(self, config)

    def _build_query(self, query_config: QueryConfig) -> str:
        """根据 QueryConfig 构建 SQL 查询。"""
        if query_config.sql_query:
            return query_config.sql_query

        cols = ", ".join(query_config.columns) if query_config.columns else "*"
        query = f"SELECT {cols} FROM {query_config.table}"
        # TODO: 在这里可以添加更多基于 `query_config` 的 WHERE 条件构造逻辑
        if query_config.limit:
            query += f" LIMIT {query_config.limit}"
        return query

    @override
    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """使用服务器端游标进行流式提取。"""
        await self._connect()
        if not self.connection:
            raise ConnectionError("PostgreSQL connection not established.")

        query = self._build_query(query_config)
        logger.info(f"Executing query: {query}")

        buffer: list[dict[str, Any]] = []
        total_rows_processed = 0

        try:
            # 使用 asyncpg 的 cursor 进行流式处理
            async with self.connection.transaction():
                async for record in self.connection.cursor(query):
                    # 将 record 转换为 dict
                    row_dict = dict(record)
                    buffer.append(row_dict)
                    total_rows_processed += 1

                    # 当缓冲区达到指定大小时，立即返回数据块
                    if len(buffer) >= export_config.parquet_chunk_size:
                        df = pd.DataFrame.from_records(buffer)
                        logger.info(
                            f"Yielding chunk: {len(buffer)} rows, total processed: {total_rows_processed}"
                        )
                        yield df
                        buffer.clear()

            # 处理剩余的数据
            if buffer:
                df = pd.DataFrame.from_records(buffer)
                logger.info(
                    f"Yielding final chunk: {len(buffer)} rows, total processed: {total_rows_processed}"
                )
                yield df

        finally:
            await self.close()


class PostgresLoader(PostgresConnector, Loader):
    """PostgreSQL 数据加载器。"""

    def __init__(self, config: PostgresConfig):
        PostgresConnector.__init__(self, config)
        Loader.__init__(self, config)

    @override
    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """加载数据块到 PostgreSQL。"""
        await self._connect()
        if not self.connection or data.empty:
            return

        columns = ", ".join(f'"{col}"' for col in data.columns)
        conflict_target_str = (
            ", ".join(f'"{col}"' for col in import_config.conflict_target)
            if import_config.conflict_target
            else ""
        )

        if import_config.conflict_strategy == ConflictStrategy.UPSERT:
            update_cols = ", ".join(
                f'"{col}" = EXCLUDED."{col}"'
                for col in data.columns
                if col not in import_config.conflict_target
            )
            if not update_cols:  # All columns are part of the conflict key
                # Fallback to DO NOTHING if there are no columns to update
                sql = f"""
                    INSERT INTO {import_config.table_name} ({columns})
                    VALUES %s
                    ON CONFLICT ({conflict_target_str}) DO NOTHING;
                """
            else:
                sql = f"""
                    INSERT INTO {import_config.table_name} ({columns})
                    VALUES %s
                    ON CONFLICT ({conflict_target_str}) DO UPDATE SET {update_cols};
                """
        else:  # 'ignore'
            sql = f"""
                INSERT INTO {import_config.table_name} ({columns})
                VALUES %s
                ON CONFLICT DO NOTHING;
            """

        try:
            # 将 DataFrame 转换为记录列表
            records = data.to_records(index=False).tolist()

            # 使用 execute 方法执行带有 ON CONFLICT 的 SQL
            await self.connection.executemany(sql, records)

            logger.info(
                f"Loaded {len(data)} rows into '{import_config.table_name}' using '{import_config.conflict_strategy}' strategy."
            )
        except asyncpg.PostgresError as e:
            logger.error(f"Error loading data into PostgreSQL: {e}")
            raise
