# 异步S3工具使用指南

本文档介绍如何使用异步S3工具模块进行文件上传、下载和管理操作。

## 概述

异步S3工具模块提供了以下功能：

- 异步文件上传到S3
- 异步文件从S3下载
- 异步列出S3对象
- 异步删除S3对象
- 批量文件上传（支持并发控制）
- 异步S3客户端（支持上下文管理器）
- S3生命周期策略管理

## 基本用法

### 1. 单个文件上传

```python
import asyncio
from datax.models.config import S3Config
from datax.utils.s3 import upload_file_to_s3

async def upload_single_file():
    s3_config = S3Config(
        bucket="my-bucket",
        access_key="your-access-key",
        secret_key="your-secret-key",
        max_concurrency=10,
        max_bandwidth_mbps=50
    )
    
    await upload_file_to_s3(
        local_path="local_file.txt",
        s3_config=s3_config,
        s3_key="remote/path/file.txt"
    )

# 运行
asyncio.run(upload_single_file())
```

### 2. 单个文件下载

```python
from datax.utils.s3 import download_file_from_s3

async def download_single_file():
    await download_file_from_s3(
        s3_config=s3_config,
        s3_key="remote/path/file.txt",
        local_path="downloaded_file.txt"
    )
```

### 3. 列出S3对象

```python
from datax.utils.s3 import list_s3_objects

async def list_objects():
    async for obj_key in list_s3_objects(s3_config, prefix="data/"):
        print(f"Found object: {obj_key}")
```

### 4. 删除S3对象

```python
from datax.utils.s3 import delete_s3_object

async def delete_object():
    await delete_s3_object(s3_config, "remote/path/file.txt")
```

## 高级用法

### 1. 批量文件上传

```python
from datax.utils.s3 import upload_multiple_files_to_s3

async def batch_upload():
    file_mappings = [
        ("local_file1.txt", "remote/file1.txt"),
        ("local_file2.txt", "remote/file2.txt"),
        ("local_file3.txt", "remote/file3.txt"),
    ]
    
    await upload_multiple_files_to_s3(
        file_mappings=file_mappings,
        s3_config=s3_config,
        max_concurrent_uploads=3  # 最多3个并发上传
    )
```

### 2. 使用异步S3客户端

```python
from datax.utils.s3 import AsyncS3Client

async def use_async_client():
    async with AsyncS3Client(s3_config) as client:
        # 上传文件
        await client.upload_file("local_file.txt", "remote_file.txt")
        
        # 下载文件
        await client.download_file("remote_file.txt", "downloaded_file.txt")
        
        # 删除文件
        await client.delete_object("remote_file.txt")
        
        # 列出对象
        async for obj_key in client.list_objects(prefix="data/"):
            print(f"Object: {obj_key}")
```

### 3. S3生命周期策略管理

```python
from datax.utils.s3 import apply_s3_lifecycle_policy

async def manage_lifecycle():
    # 生命周期配置文件示例 (lifecycle_config.yaml):
    # rules:
    #   - id: "delete-old-files"
    #     prefix: "logs/"
    #     expiration_days: 30
    #   - id: "archive-data"
    #     prefix: "data/"
    #     expiration_days: 365
    
    await apply_s3_lifecycle_policy(
        bucket_name="my-bucket",
        config_path="lifecycle_config.yaml"
    )
```

## 与Prefect集成

### 1. 在Prefect Flow中使用

```python
from prefect import flow, task
from datax.utils.s3 import upload_file_to_s3, AsyncS3Client

@task
async def upload_to_s3_task(local_path: str, s3_key: str, s3_config: S3Config):
    await upload_file_to_s3(local_path, s3_config, s3_key)
    return f"s3://{s3_config.bucket}/{s3_key}"

@task
async def process_with_s3_client(s3_config: S3Config):
    async with AsyncS3Client(s3_config) as client:
        # 执行S3操作
        await client.upload_file("processed_data.txt", "results/data.txt")
        return "Processing completed"

@flow
async def data_processing_flow():
    s3_config = S3Config(
        bucket="data-bucket",
        access_key="access-key",
        secret_key="secret-key"
    )
    
    # 上传原始数据
    s3_path = await upload_to_s3_task("raw_data.txt", "input/data.txt", s3_config)
    
    # 处理数据
    result = await process_with_s3_client(s3_config)
    
    return result

# 运行flow
if __name__ == "__main__":
    asyncio.run(data_processing_flow())
```

### 2. 错误处理和重试

```python
from prefect import task
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
@task
async def robust_s3_upload(local_path: str, s3_key: str, s3_config: S3Config):
    try:
        await upload_file_to_s3(local_path, s3_config, s3_key)
        return True
    except Exception as e:
        logger.error(f"Upload failed: {e}")
        raise
```

## 性能优化

### 1. 并发控制

```python
# 根据网络带宽调整并发数
s3_config = S3Config(
    bucket="my-bucket",
    access_key="access-key",
    secret_key="secret-key",
    max_concurrency=20,  # 高带宽网络可以增加并发数
    max_bandwidth_mbps=100  # 限制带宽使用
)
```

### 2. 批量操作优化

```python
# 对于大量文件，使用批量上传
async def optimize_batch_upload():
    # 将文件分批处理
    batch_size = 100
    all_files = [("file1.txt", "key1.txt"), ...]  # 大量文件
    
    for i in range(0, len(all_files), batch_size):
        batch = all_files[i:i + batch_size]
        await upload_multiple_files_to_s3(
            file_mappings=batch,
            s3_config=s3_config,
            max_concurrent_uploads=10
        )
```

## 配置说明

### S3Config参数

- `bucket`: S3桶名称
- `access_key`: AWS访问密钥
- `secret_key`: AWS秘密密钥
- `max_concurrency`: 传输的最大并发线程数（默认10）
- `max_bandwidth_mbps`: 传输的最大带宽，单位MB/s（默认10）

### 环境变量配置

```python
import os
from datax.models.config import S3Config

s3_config = S3Config(
    bucket=os.getenv("S3_BUCKET"),
    access_key=os.getenv("AWS_ACCESS_KEY_ID"),
    secret_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
    max_concurrency=int(os.getenv("S3_MAX_CONCURRENCY", "10")),
    max_bandwidth_mbps=int(os.getenv("S3_MAX_BANDWIDTH_MBPS", "10"))
)
```

## 注意事项

1. **异步上下文**: 所有函数都是异步的，必须在异步环境中调用
2. **资源管理**: 使用`AsyncS3Client`时，会自动管理连接资源
3. **错误处理**: 建议在调用S3操作时添加适当的错误处理
4. **网络配置**: 根据网络环境调整并发数和带宽限制
5. **安全性**: 不要在代码中硬编码AWS凭证，使用环境变量或IAM角色

## 测试

运行异步S3工具的测试：

```bash
# 运行所有S3相关测试
pytest tests/unit/test_s3_async.py -v

# 运行特定测试
pytest tests/unit/test_s3_async.py::TestAsyncS3Utils::test_upload_file_to_s3_success -v
``` 