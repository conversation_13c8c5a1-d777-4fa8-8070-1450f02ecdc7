"""
数据提取器抽象基类模块。

本模块定义了数据提取器的抽象接口，所有具体的数据库提取器都必须继承并实现该接口。
提供了异步上下文管理器支持，确保数据库连接的正确建立和释放。
核心设计采用流式处理模式，支持处理大规模数据集而不会耗尽内存。

主要功能：
- 定义标准的数据提取器接口
- 提供异步上下文管理器支持
- 实现流式数据提取机制
- 确保资源的正确管理和释放
"""

from abc import ABC, abstractmethod
from collections.abc import AsyncIterator
from types import TracebackType
from typing import Self

import pandas as pd

from ..models import ConnectionConfig, ExportConfig, QueryConfig


class Extractor(ABC):
    """数据提取器接口定义。"""

    def __init__(self, config: ConnectionConfig):
        """
        使用连接配置初始化提取器。

        :param config: 包含数据库连接信息的 Pydantic 模型。
        """
        self.config = config
        self.connection: object | None = None
        self.cursor: object | None = None

    @abstractmethod
    async def _connect(self) -> None:
        """建立数据库连接的内部方法。"""
        raise NotImplementedError

    @abstractmethod
    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """
        核心方法，以流式方式提取数据并产生 pandas DataFrame 块。

        此方法必须实现为生成器，逐块（chunk）产生 pandas DataFrame。
        这种设计对于处理亿级记录至关重要，因为它避免了将整个数据集加载到内存中。

        :param query_config: 包含查询条件（如 SQL、范围、字段列表）的 Pydantic 模型。
        :param export_config: 包含批次大小等导出配置的 Pydantic 模型。
        :yield: pandas DataFrame 数据块。
        """
        raise NotImplementedError

    @abstractmethod
    async def close(self) -> None:
        """
        关闭连接和游标，释放所有数据库资源。

        必须确保此方法在任何情况下（成功或异常）都能被调用，
        以防止资源泄漏。
        """
        raise NotImplementedError

    async def __aenter__(self) -> Self:
        await self._connect()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        await self.close()
